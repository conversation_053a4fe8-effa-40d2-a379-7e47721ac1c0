import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextRequest } from 'next/server';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json();

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      system: `You are a helpful AI assistant that can interact with Multiple MCP (Model Context Protocol) servers.
      
You have access to various tools and capabilities through MCP servers including:
- File operations and code analysis
- Web search and research
- Package management and documentation
- Project management and task tracking

When users ask questions or request help, you can leverage these MCP capabilities to provide comprehensive assistance.`,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
